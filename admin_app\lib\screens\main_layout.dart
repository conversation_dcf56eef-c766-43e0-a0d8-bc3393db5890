import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/navigation_models.dart';
import '../services/navigation_service.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final Function(String)? onNavigationItemTapped;

  const MainLayout({
    Key? key,
    required this.child,
    required this.currentRoute,
    this.onNavigationItemTapped,
  }) : super(key: key);

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  bool _isDrawerOpen = false; // Changed to false - drawer closed by default
  final double _drawerWidth = 260;
  final double _collapsedDrawerWidth = 72;

  // Check if the screen is mobile size
  bool get _isMobile => MediaQuery.of(context).size.width < 768;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ensure drawer is closed by default on mobile
    if (_isMobile && _isDrawerOpen) {
      setState(() {
        _isDrawerOpen = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final navigationService = Provider.of<NavigationService>(context);
    final navigationGroups = navigationService.getNavigationItems();
    final currentItem = navigationService.getAllNavigationItems().firstWhere(
          (item) => item.route == widget.currentRoute,
          orElse: () => NavigationItem(
            icon: Icons.error_outline,
            activeIcon: Icons.error,
            label: 'Error',
            index: -1,
          ),
        );

    return Scaffold(
      body: Stack(
        children: [
          // Main content area
          Row(
            children: [
              // Navigation Drawer (only show on desktop or when open on mobile)
              if (!_isMobile || _isDrawerOpen)
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: _isDrawerOpen ? _drawerWidth : _collapsedDrawerWidth,
                  color: Theme.of(context).colorScheme.surface,
            child: Column(
              children: [
                // App Header
                Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor.withOpacity(0.08),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      if (_isDrawerOpen) ...[
                        const Icon(
                          Icons.admin_panel_settings,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Admin Panel',
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(color: Colors.white),
                        ),
                      ],
                    ],
                  ),
                ),
                // Navigation Items
                Expanded(
                  child: ListView.builder(
                    itemCount: navigationGroups.length,
                    itemBuilder: (context, groupIndex) {
                      final group = navigationGroups[groupIndex];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_isDrawerOpen && group.title.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 16,
                                top: 16,
                                bottom: 8,
                              ),
                              child: Text(
                                group.title.toUpperCase(),
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color
                                          ?.withOpacity(0.7),
                                      letterSpacing: 0.5,
                                    ),
                              ),
                            ),
                          ...group.items.map((item) {
                            final isSelected =
                                item.route == widget.currentRoute;
                            return ListTile(
                              leading: Icon(
                                isSelected ? item.activeIcon : item.icon,
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.8),
                              ),
                              title: _isDrawerOpen
                                  ? Text(
                                      item.label,
                                      style: TextStyle(
                                        color: isSelected
                                            ? Theme.of(context)
                                                .colorScheme
                                                .primary
                                            : null,
                                        fontWeight: isSelected
                                            ? FontWeight.w600
                                            : null,
                                      ),
                                    )
                                  : null,
                              minLeadingWidth: 24,
                              dense: true,
                              onTap: () {
                                if (item.route != null) {
                                  // Close drawer on mobile after navigation
                                  if (_isMobile && _isDrawerOpen) {
                                    setState(() {
                                      _isDrawerOpen = false;
                                    });
                                  }

                                  if (widget.onNavigationItemTapped != null) {
                                    widget.onNavigationItemTapped!(item.route!);
                                  } else {
                                    Navigator.of(context).pushReplacementNamed(item.route!);
                                  }
                                }
                              },
                            );
                          }).toList(),
                          if (groupIndex < navigationGroups.length - 1)
                            const Divider(height: 1, thickness: 1),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Main Content
          Expanded(
            child: Container(
              margin: EdgeInsets.only(
                left: _isMobile ? 0 : (_isDrawerOpen ? 0 : _collapsedDrawerWidth),
              ),
              child: Column(
                children: [
                // Top App Bar
                Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor.withOpacity(0.08),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: Icon(
                          _isDrawerOpen
                              ? Icons.menu_open
                              : Icons.menu,
                        ),
                        onPressed: () {
                          setState(() {
                            _isDrawerOpen = !_isDrawerOpen;
                          });
                        },
                      ),
                      const SizedBox(width: 16),
                      Text(
                        currentItem.label,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const Spacer(),
                      // Add user profile and notifications here
                    ],
                  ),
                ),
                // Page Content
                Expanded(
                  child: widget.child,
                ),
                ],
              ),
          ),
            ],
          ),

          // Mobile overlay when drawer is open
          if (_isMobile && _isDrawerOpen)
            GestureDetector(
              onTap: () {
                setState(() {
                  _isDrawerOpen = false;
                });
              },
              child: Container(
                color: Colors.black54,
              ),
            ),

          // Floating action button for mobile when drawer is closed
          if (_isMobile && !_isDrawerOpen)
            Positioned(
              left: 16,
              bottom: 16,
              child: FloatingActionButton(
                heroTag: 'menu_toggle',
                onPressed: () {
                  setState(() {
                    _isDrawerOpen = true;
                  });
                },
                child: const Icon(Icons.menu),
                backgroundColor: Theme.of(context).primaryColor,
              ),
            ),
        ],
      ),
    );
  }
}
